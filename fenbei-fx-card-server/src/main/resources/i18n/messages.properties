CardApplyStatusEnum.WAITCHECK=\u5F85\u5BA1\u6838
CardApplyStatusEnum.PENDING=\u5BA1\u6838\u4E2D
CardApplyStatusEnum.PASS=\u5BA1\u6838\u901A\u8FC7
CardApplyStatusEnum.REFUSE=\u5BA1\u6838\u62D2\u7EDD
CardApplyStatusEnum.FAILED=\u521B\u5EFA\u5931\u8D25
CardApplyStatusEnum.BANKPASS=\u94F6\u884C\u6210\u529F
CardApplyStatusEnum.STEREO_PENDING=\u5F85\u90AE\u5BC4
card.issue.to.organisation=\u4F01\u4E1A
card.issue.to.individual=\u4E2A\u4EBA
apply.type.add=\u521B\u5EFA
apply.type.update=\u66F4\u65B0
cardholder.show.status.effective=\u751F\u6548\u4E2D
cardholder.show.status.expired=\u5DF2\u5931\u6548
cardholder.apply.status.applying=\u5F85\u5BA1\u6838
cardholder.apply.status.pass=\u5BA1\u6838\u901A\u8FC7
cardholder.apply.status.refuse=\u5BA1\u6838\u62D2\u7EDD
cardholder.apply.status.bank.dealing=\u94F6\u884C\u5904\u7406\u4E2D
cardholder.apply.status.bank.refuse=\u94F6\u884C\u5931\u8D25
cardholder.apply.status.bank.pass=\u94F6\u884C\u6210\u529F
CardShowStatusEnum.WAITCHECK=\u5F85\u5BA1\u6838
CardShowStatusEnum.PENDING=\u5BA1\u6838\u4E2D
CardShowStatusEnum.OPER_FAILED=\u521B\u5EFA\u5931\u8D25
CardShowStatusEnum.ACTIVE=\u751F\u6548\u4E2D
CardShowStatusEnum.DISABLE=\u5DF2\u5931\u6548
CardShowStatusEnum.REFUSE=\u5DF2\u62D2\u7EDD
CardShowStatusEnum.WAITMAIL=\u5F85\u90AE\u5BC4
CardShowStatusEnum.OTHER=\u5176\u5B83
cardholder.status.effective=\u751F\u6548\u4E2D
cardholder.status.disabled=\u5DF2\u7981\u7528
cardholder.status.expired=\u5DF2\u5931\u6548
cardholder.airwallex.status.pending=\u5BA1\u6838\u4E2D
cardholder.airwallex.status.disabled=\u5DF2\u5931\u6548
cardholder.airwallex.status.ready=\u751F\u6548\u4E2D
purpose.other=\u5176\u4ED6
purpose.business.expenses=\u4E1A\u52A1\u8D39\u7528
purpose.client.expenses=\u5BA2\u52E4\u8D39\u7528
purpose.marketing.expenses=\u8425\u9500\u8D39\u7528
purpose.office.supplies=\u529E\u516C\u91C7\u8D2D
purpose.online.purchasing=\u7F51\u4E0A\u8D2D\u7269
purpose.subscriptions=\u8BA2\u9605\u670D\u52A1
purpose.team.expenses=\u56E2\u5EFA\u8D39\u7528
purpose.travel.expenses=\u5DEE\u65C5\u8D39\u7528
cardholder.oper.type.edit=\u7F16\u8F91
cardholder.oper.type.delete=\u5220\u9664
cardholder.oper.type.disable=\u7981\u7528
cardholder.oper.type.open=\u542F\u7528
cardholder.oper.type.approve=\u5BA1\u6838
cardholder.oper.type.detail=\u8BE6\u60C5
cardholder.apply.show.status.applying=\u5F85\u5BA1\u6838
cardholder.apply.show.status.updating=\u66F4\u65B0\u4E2D
cardholder.apply.show.status.updating.failed=\u66F4\u65B0\u5931\u8D25
card.status.waitcheck=\u5F85\u5BA1\u6838
card.status.pending=\u5BA1\u6838\u4E2D
card.status.active=\u751F\u6548\u4E2D
card.status.disable=\u5DF2\u7981\u7528
card.status.loss=\u6302\u5931
card.status.stolen=\u88AB\u76D7
card.status.failed=\u66F4\u65B0\u5931\u8D25
card.status.logout=\u5DF2\u6CE8\u9500
card.status.freeze=\u51BB\u7ED3
card.status.refuse=\u5BA1\u6838\u62D2\u7EDD
delete.flag.normal=\u6B63\u5E38
delete.flag.del=\u5220\u9664
card.form.factor.physical=\u5B9E\u4F53\u5361
card.form.factor.virtual=\u865A\u62DF\u5361
trade.limit.per.transaction=\u5355\u7B14\u4EA4\u6613
trade.limit.daily=\u65E5\u9650\u989D
trade.limit.weekly=\u5468\u9650\u989D
trade.limit.monthly=\u6708\u9650\u989D
trade.limit.all.time=\u603B\u9650\u989D
identification.expiry.type.scope.time=\u65F6\u95F4\u8303\u56F4
identification.expiry.type.long.time=\u957F\u671F\u6709\u6548
identification.type.id.card=\u8EAB\u4EFD\u8BC1
identification.type.passport=\u62A4\u7167
identification.type.drivers.license=\u9A7E\u7167
card.active.status.not.require=\u65E0\u9700\u6FC0\u6D3B
card.active.status.wait=\u5F85\u6FC0\u6D3B
card.active.status.dealing=\u6FC0\u6D3B\u4E2D
card.active.status.fail=\u6FC0\u6D3B\u5931\u8D25
card.active.status.success=\u6FC0\u6D3B\u6210\u529F
GlobalCoreResponseCode.EXCEPTION=\u7CFB\u7EDF\u9519\u8BEF
GlobalCoreResponseCode.ILLEGAL_ARGUMENT=\u53C2\u6570\u9519\u8BEF
GlobalCoreResponseCode.SERVER_REQUEST_FAST=\u8D85\u8FC7\u8BF7\u6C42\u6B21\u6570\u9650\u5236
GlobalCoreResponseCode.RemoteService401=\u8BBF\u95EE\u9700\u8981\u8EAB\u4EFD\u8BA4\u8BC1
GlobalCoreResponseCode.BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST2=\u60A8\u5DF2\u88AB\u79FB\u9664\u4F01\u4E1A\uFF0C\u65E0\u6CD5\u7533\u8BF7\u4F01\u4E1A\u88AB\u7981\u7528
GlobalCoreResponseCode.BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST3=\u60A8\u7684\u8D26\u53F7\u5DF2\u88AB\u4F01\u4E1A\u7981\u7528\uFF0C\u65E0\u6CD5\u7533\u8BF7\u4F01\u4E1A\u88AB\u7981\u7528
GlobalCoreResponseCode.PHONE_EXIST_EXCEPTION=\u8BE5\u624B\u673A\u53F7\u5DF2\u6CE8\u518C\u8FC7\uFF0C\u8BF7\u66F4\u6362\u624B\u673A\u53F7\u91CD\u8BD5
GlobalCoreResponseCode.EMAIL_EXIST_EXCEPTION=\u8BE5\u90AE\u7BB1\u5DF2\u6CE8\u518C\u8FC7\uFF0C\u8BF7\u66F4\u6362\u90AE\u7BB1\u91CD\u8BD5
GlobalCoreResponseCode.EMAIL_FORMAT_ERROR=\u8BE5\u90AE\u7BB1\u683C\u5F0F\u6709\u8BEF\uFF0C\u8BF7\u4FEE\u6539\u540E\u91CD\u8BD5
GlobalCoreResponseCode.EMAIL_DOMAIN_NOT_ALLOWED=\u90ae\u7bb1\u8bf7\u586b\u5199\u60a8\u7684\u4e2a\u4eba\u90ae\u7bb1\u5730\u5740\uff0c\u5982\uff1a\u0040\u0071\u0071\u3001\u0040\u0031\u0036\u0033\u7b49\u516c\u5171\u90ae\u7bb1\u3002\u4f01\u4e1a\u90ae\u7bb1\uff08\u0040\u516c\u53f8\u57df\u540d\uff09\u65e0\u6cd5\u7528\u4e8e\u6b64\u64cd\u4f5c\uff0c\u8bf7\u52ff\u4f7f\u7528
GlobalCoreResponseCode.APPLY_NOT_EXIST=\u8BE5\u7533\u8BF7\u5DF2\u4E0D\u5B58\u5728\u6216\u72B6\u6001\u5DF2\u6539\u53D8\uFF01
GlobalCoreResponseCode.APPLY_APPROVE_STATUS_ERROR=\u4F20\u9012\u7684\u5BA1\u6279\u72B6\u6001\u4E0D\u5BF9\uFF01
GlobalCoreResponseCode.CARDHOLDER_NOT_EXIST=\u6301\u5361\u4EBA\u4FE1\u606F\u4E0D\u5B58\u5728\uFF01
GlobalCoreResponseCode.CARDHOLDER_UPDATE_ERROR=\u6301\u5361\u4EBA\u66F4\u65B0\u5931\u8D25\uFF01
GlobalCoreResponseCode.COMMIT_CARDHOLDER_UPDATE_ERROR=\u63D0\u4EA4\u6301\u5361\u4EBA\u66F4\u65B0\u5931\u8D25\uFF01
GlobalCoreResponseCode.CARDHOLDER_STATUS_NOT_SUPPORT_UPDATE=\u5F53\u524D\u72B6\u6001\u4E0D\u80FD\u66F4\u65B0\uFF01
GlobalCoreResponseCode.CARDHOLDER_ENABLE_SUPPORT_UPDATE=\u6301\u5361\u4EBA\u72B6\u6001\u66F4\u65B0\u5931\u8D25\uFF01
GlobalCoreResponseCode.APPROVE_NO_AUTH=\u65E0\u6743\u9650\u64CD\u4F5C\u6B64\u6761\u6570\u636E\uFF01
GlobalCoreResponseCode.ADDRESS_NOT_WHOLE_ERROR=\u5730\u5740\u4FE1\u606F\u4E0D\u5168\uFF01
GlobalCoreResponseCode.ADDRESS_NOT_SUPPORT_ERROR=\u6682\u4E0D\u652F\u6301\u7684\u5DDE\u4FE1\u606F\uFF01
GlobalCoreResponseCode.OPERATION_OFTEN=\u64CD\u4F5C\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5
GlobalCoreResponseCode.CARDHOLDER_HAS_EXIST=\u6301\u5361\u4EBA\u4FE1\u606F\u5DF2\u5B58\u5728\uFF0C\u4E0D\u80FD\u91CD\u590D\u7533\u8BF7
GlobalCoreResponseCode.CARDHOLDERAPPLY_HAS_EXIST=\u6301\u5361\u4EBA\u7533\u8BF7\u4FE1\u606F\u5DF2\u5B58\u5728\uFF0C\u4E0D\u80FD\u91CD\u590D\u7533\u8BF7
GlobalCoreResponseCode.CARDHOLDERAPPLY_EXPIRY_DATE_ERROR=\u8BC1\u4EF6\u8FC7\u671F\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
GlobalCoreResponseCode.CARDHOLDERAPPLY_EXPIRY_DATE_FORMAT_ERROR=\u8BC1\u4EF6\u8FC7\u671F\u65F6\u95F4\u683C\u5F0F\u4E0D\u6B63\u786E
GlobalCoreResponseCode.CARDHOLDERAPPLY_ERROR=\u7528\u6237\u6301\u5361\u4EBA\u7533\u8BF7\u4FE1\u606F\u5B58\u5728\u5F02\u5E38
GlobalCoreResponseCode.CARDHOLDERAPPLY_IS_BANK_DEALING=\u5F53\u524D\u6301\u5361\u4EBA\u4FE1\u606F\u5DF2\u7ECF\u5728\u6E20\u9053\u5904\u7406\u4E2D\u6682\u65F6\u4E0D\u80FD\u66F4\u65B0
GlobalCoreResponseCode.CARDHOLDERAPPLY_UPDATE_HAS_EXIST=\u5F53\u524D\u6301\u5361\u4EBA\u5B58\u5728\u8FDB\u884C\u4E2D\u7684\u66F4\u65B0\u4FE1\u606F\uFF0C\u8BF7\u5B8C\u6210\u540E\u518D\u63D0\u4EA4
GlobalCoreResponseCode.CARDHOLDERAPPLY_UPDATE_ERROR=\u66F4\u65B0\u6301\u5361\u4EBA\u4FE1\u606F\u5931\u8D25
GlobalCoreResponseCode.CARD_HOLDER_NOT_EXIT=\u6301\u5361\u4EBA\u4E0D\u5B58\u5728\u8BF7\u5148\u521B\u5EFA\u6301\u5361\u4EBA
GlobalCoreResponseCode.CARD_APPLY_EXIT=\u5F53\u524D\u5361\u6B63\u5728\u5BA1\u6838\u4E2D\u8BF7\u52FF\u91CD\u590D\u63D0\u4EA4
GlobalCoreResponseCode.CARD_APPLY_CREATE_ERROR=\u521B\u5EFA\u5361\u4FE1\u606F\u5931\u8D25
GlobalCoreResponseCode.CARD_APPLY_UPDATE_ERROR=\u66F4\u65B0\u5361\u4FE1\u606F\u5931\u8D25
GlobalCoreResponseCode.CARD_NOT_EXIT=\u5361\u7247\u4E0D\u5B58\u5728
GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR=\u5361\u7247\u72B6\u6001\u64CD\u4F5C\u5931\u8D25
GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO=\u5F53\u524D\u5361\u7247\u4E0D\u53EF\u4EE5\u8FDB\u884C\u6B64\u64CD\u4F5C
GlobalCoreResponseCode.AIR_REQUEST_STATUS_NOT_MATCH=\u64CD\u4F5C\u540E\u72B6\u6001\u5F02\u5E38
GlobalCoreResponseCode.COMPANY_ACCOUNT_NOT_EXIT=\u4F01\u4E1A\u8D26\u6237\u4E0D\u5B58\u5728
GlobalCoreResponseCode.COMPANY_ACCOUNT_STATUS_ERROR=\u4F01\u4E1A\u8D26\u6237\u4E0D\u53EF\u7528
GlobalCoreResponseCode.COMPANY_ACCOUNT_BALANCE_NOT_ENOUGH=\u4F01\u4E1A\u8D26\u6237\u53EF\u7528\u4F59\u989D\u4E0D\u8DB3
GlobalCoreResponseCode.CARD_HOLDER_NOT_PASS=\u60a8\u7684\u6301\u5361\u4eba\u4fe1\u606f\u5df2\u540c\u6b65\u81f3\u4f01\u4e1a\u7ba1\u7406\u5458\uff0c\u8bf7\u8054\u7cfb\u4f01\u4e1a\u7ba1\u7406\u5458\u5148\u5bf9\u60a8\u7684\u6301\u5361\u4eba\u4fe1\u606f\u4f5c\u5ba1\u6838\uff0c\u5ba1\u6838\u901a\u8fc7\u540e\u624d\u53ef\u7533\u8bf7\u5b9e\u4f53\u5361\uff01
IN_PARAMS_NOT_EMPTY=\u5165\u53C2\u4E0D\u80FD\u4E3A\u7A7A
COLL_SIZE_NOT_EMPTY=\u96C6\u5408\u4E0D\u80FD\u4E3A\u7A7A\u4E14\u5927\u5C0F\u5927\u4E8E0
NO_FIND_RECORDS=\u672A\u67E5\u627E\u5230\u8BB0\u5F55
CM_DJ_JY_ZT_CAN_ENABLE=\u51BB\u7ED3/\u7981\u7528\u72B6\u6001\u53EF\u4EE5\u542F\u7528
CM_SXZ_CAN_DISABLE=\u751F\u6548\u4E2D\u7684\u5361\u624D\u53EF\u4EE5\u7981\u7528
CM_SHZ_SXZ_DJ_CAN_ZX=\u5BA1\u6838\u4E2D/\u751F\u6548\u4E2D/\u51BB\u7ED3\u624D\u53EF\u4EE5\u6CE8\u9500
CAM_NOT_EMPTY=\u4E0D\u80FD\u4E3A\u7A7A
i18n.wuxu.hexiao=\u65E0\u9700\u6838\u9500
i18n.yi.tuikuan=\u5DF2\u9000\u6B3E
i18n.bufen.daihexiao=\u90E8\u5206\u5F85\u6838\u9500
i18n.dai.hexiao=\u5F85\u6838\u9500
i18n.hexiao.zhong=\u6838\u9500\u4E2D
i18n.yi.hexiao=\u5DF2\u6838\u9500
i18n.weizhi=\u672A\u77E5
i18n.yuding=\u9884\u6388\u6743
i18n.xiaofei=\u6D88\u8D39
i18n.tuikuan=\u9000\u6B3E
i18n.yuding.shifang=\u9884\u6388\u6743\u64a4\u9500
i18n.jiaoyi.shibai=\u4EA4\u6613\u5931\u8D25
i18n.huankuan=\u8FD8\u6B3E
i18n.qiye.fafang=\u4F01\u4E1A\u53D1\u653E
i18n.tuihuan.edu=\u9000\u8FD8\u989D\u5EA6
ActiveModelEnum.NORMAL=\u666E\u901A\u6A21\u5F0F
ActiveModelEnum.PETTY=\u5907\u7528\u91D1\u6A21\u5F0F
CardModelEnum.UN_KNOW=\u672A\u77E5\u64CD\u4F5C
CardModelEnum.NORMAL=\u666E\u901A\u6A21\u5F0F
CardModelEnum.PETTY=\u5907\u7528\u91D1\u6A21\u5F0F
CreditApplyStatusEnum.SUCCESS=\u4E0B\u53D1\u6210\u529F
CreditApplyStatusEnum.FAIL=\u4E0B\u53D1\u5931\u8D25
CreditApplyStatusEnum.PROCESS=\u4E0B\u53D1\u4E2D
CreditApplyStatusEnum.INIT=\u5F85\u4E0B\u53D1
CardFlowOperationTypeEnum.UNKNOWN=\u672A\u77E5
CardFlowOperationTypeEnum.CREDIT_APPLY=\u7533\u8BF7\u989D\u5EA6
CardFlowOperationTypeEnum.CREDIT_RETURN=\u4E3B\u52A8\u9000\u8FD8
CardFlowOperationTypeEnum.FROZEN=\u51BB\u7ED3
CardFlowOperationTypeEnum.UNFROZEN=\u89E3\u51BB
CardFlowOperationTypeEnum.CONSUME=\u6D88\u8D39
CardFlowOperationTypeEnum.REFUND=\u9000\u6B3E
CardFlowOperationTypeEnum.RECTIFICATION=\u51B2\u6B63
CardFlowOperationTypeEnum.REPAYMENT=\u8FD8\u6B3E
CardFlowOperationTypeEnum.CREDIT_COMPANY_RECYCLE=\u4F01\u4E1A\u56DE\u6536
CardFlowOperationTypeEnum.CREDIT_SYSTEM_RECYCLE=\u7CFB\u7EDF\u56DE\u6536
CardFlowOperationTypeEnum.REPAYMENT_RETURN=\u8FD8\u6B3E\u9000\u56DE
i18n.cunzaishenpizhong.fail=\u5B58\u5728\u5BA1\u6279\u4E2D\u7684\u7533\u8BF7\u5355\uFF0C\u4E0D\u5141\u8BB8\u63D0\u4EA4\u65B0\u7684\u7533\u8BF7\u5355
i18n.cunzaiweihexiao.fail=\u5B58\u5728\u672A\u6838\u9500\u5B8C\u6210\u7684\u5355\u636E\uFF0C\u4E0D\u5141\u8BB8\u63D0\u4EA4\u65B0\u7684\u7533\u8BF7\u5355
i18n.apply.success=\u7533\u8BF7\u6210\u529F
cost.none=\u6dfb\u52a0\u8d39\u7528
cost.bind=\u5df2\u521b\u5efa\u8d39\u7528
cost.not.commit=\u5df2\u521b\u5efa\u6838\u9500\u5355\uff0c\u672a\u63d0\u4ea4\u5ba1\u6279
cost.pending=\u6838\u9500\u5355\u5ba1\u6279\u4e2d
cost.pass=\u6838\u9500\u5355\u5ba1\u6279\u901a\u8fc7
i18n.trade.yushouquan=\u9884\u6388\u6743
i18n.trade.yushouquanchexiao=\u9884\u6388\u6743\u64a4\u9500
i18n.trade.jiaoyishibai=\u4ea4\u6613\u5931\u8d25
i18n.trade.xiaofei=\u6d88\u8d39
i18n.trade.tuikuan=\u9000\u6b3e
i18n.trade.cuohuahuankuan=\u9519\u82b1\u8fd8\u6b3e
