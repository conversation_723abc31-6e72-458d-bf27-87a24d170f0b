CardApplyStatusEnum.WAITCHECK=To Be Reviewed
CardApplyStatusEnum.PENDING=Under Approve
CardApplyStatusEnum.PASS=Approved
CardApplyStatusEnum.REFUSE=Rejected
CardApplyStatusEnum.FAILED=Failed
CardApplyStatusEnum.BANKPASS=Bank Success
CardApplyStatusEnum.STEREO_PENDING=Under Mail
card.issue.to.organisation=Company
card.issue.to.individual=Personal
apply.type.add=Create
apply.type.update=Update
cardholder.show.status.effective=Active
cardholder.show.status.expired=Expired
cardholder.apply.status.applying=To Be Reviewed
cardholder.apply.status.pass=Approved
cardholder.apply.status.refuse=Rejected
cardholder.apply.status.bank.dealing=Bank Processing
cardholder.apply.status.bank.refuse=Bank Failed
cardholder.apply.status.bank.pass=Bank Success
CardShowStatusEnum.WAITCHECK=Pending
CardShowStatusEnum.PENDING=Pending
CardShowStatusEnum.OPER_FAILED=Failed
CardShowStatusEnum.ACTIVE=Active
CardShowStatusEnum.DISABLE=Blocked
CardShowStatusEnum.REFUSE=Refuse
CardShowStatusEnum.WAITMAIL=Under Mail
CardShowStatusEnum.OTHER=OTHERS
cardholder.status.effective=Active
cardholder.status.disabled=Blocked
cardholder.status.expired=Expired
cardholder.airwallex.status.pending=Under Approve
cardholder.airwallex.status.disabled=Expired
cardholder.airwallex.status.ready=Active
purpose.other=OTHERS
purpose.business.expenses=BUSINESS_EXPENSES
purpose.client.expenses=CLIENT_EXPENSES
purpose.marketing.expenses=MARKETING_EXPENSES
purpose.office.supplies=OFFICE_SUPPLIES
purpose.online.purchasing=ONLINE_PURCHASING
purpose.subscriptions=SUBSCRIPTIONS
purpose.team.expenses=TEAM_EXPENSES
purpose.travel.expenses=TRAVEL_EXPENSES
cardholder.oper.type.edit=Edit
cardholder.oper.type.delete=Delete
cardholder.oper.type.disable=Block
cardholder.oper.type.open=Active
cardholder.oper.type.approve=Approve
cardholder.oper.type.detail=Detail
cardholder.apply.show.status.applying=To Be Reviewed
cardholder.apply.show.status.updating=Updating
cardholder.apply.show.status.updating.failed=Update Failed
card.status.waitcheck=Pending
card.status.pending=Pending
card.status.active=Active
card.status.disable=Blocked
card.status.loss=Lost
card.status.stolen=Stolen
card.status.failed=Failed
card.status.logout=Closed
card.status.freeze=Inactive
card.status.refuse=Refuse
delete.flag.normal=Active
delete.flag.del=Delete
card.form.factor.physical=Physical
card.form.factor.virtual=Virtual
trade.limit.per.transaction=Per_Transaction
trade.limit.daily=Daily
trade.limit.weekly=Weekly
trade.limit.monthly=Monthly
trade.limit.all.time=All_Time
identification.expiry.type.scope.time=Time range
identification.expiry.type.long.time=Long-term effectiveness
identification.type.id.card=ID Card
identification.type.passport=Passport
identification.type.drivers.license=Driver License
card.active.status.not.require=No need active
card.active.status.wait=To be activated
card.active.status.dealing=Activating
card.active.status.fail=Active Failed
card.active.status.success=Active success
GlobalCoreResponseCode.EXCEPTION=System error
GlobalCoreResponseCode.ILLEGAL_ARGUMENT=Parameter error
GlobalCoreResponseCode.SERVER_REQUEST_FAST=Exceeded request limit
GlobalCoreResponseCode.RemoteService401=Access requires authentication.
GlobalCoreResponseCode.BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST2=You have been removed from the enterprise and cannot request the enterprise to be disabled
GlobalCoreResponseCode.BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST3=Your account has been disabled by the enterprise and you cannot request the enterprise to be disabled
GlobalCoreResponseCode.PHONE_EXIST_EXCEPTION=This phone number has already been registered, please try again with a different phone number
GlobalCoreResponseCode.EMAIL_EXIST_EXCEPTION=This email has already been registered, please try again with a different email
GlobalCoreResponseCode.EMAIL_FORMAT_ERROR=The email format is incorrect, please modify and try again
GlobalCoreResponseCode.EMAIL_DOMAIN_NOT_ALLOWED=Please provide your personal email address (e.g., public email services like @gmail, @outlook). Business emails (@yourcompanydomain) cannot be used for this operation.
GlobalCoreResponseCode.APPLY_NOT_EXIST=The application no longer exists or its status has changed!
GlobalCoreResponseCode.APPLY_APPROVE_STATUS_ERROR=The approval status passed is not correct!
GlobalCoreResponseCode.CARDHOLDER_NOT_EXIST=Cardholder information does not exist!
GlobalCoreResponseCode.CARDHOLDER_UPDATE_ERROR=Cardholder update failed!
GlobalCoreResponseCode.COMMIT_CARDHOLDER_UPDATE_ERROR=Failed to submit cardholder update!
GlobalCoreResponseCode.CARDHOLDER_STATUS_NOT_SUPPORT_UPDATE=Cannot update in current status!
GlobalCoreResponseCode.CARDHOLDER_ENABLE_SUPPORT_UPDATE=Cardholder status update failed!
GlobalCoreResponseCode.APPROVE_NO_AUTH=No authority to operate on this data!
GlobalCoreResponseCode.ADDRESS_NOT_WHOLE_ERROR=Incomplete address information!
GlobalCoreResponseCode.ADDRESS_NOT_SUPPORT_ERROR=Unsupported state information!
GlobalCoreResponseCode.OPERATION_OFTEN=Too frequent operations, please try again later
GlobalCoreResponseCode.CARDHOLDER_HAS_EXIST=Cardholder information already exists, cannot apply again
GlobalCoreResponseCode.CARDHOLDERAPPLY_HAS_EXIST=Cardholder application information already exists, cannot apply again
GlobalCoreResponseCode.CARDHOLDERAPPLY_EXPIRY_DATE_ERROR=Expiration date of the document cannot be empty
GlobalCoreResponseCode.CARDHOLDERAPPLY_EXPIRY_DATE_FORMAT_ERROR=Incorrect format for expiration date of the document
GlobalCoreResponseCode.CARDHOLDERAPPLY_ERROR=There is an abnormality in the user's cardholder application information
GlobalCoreResponseCode.CARDHOLDERAPPLY_IS_BANK_DEALING=The current cardholder information is being processed in the channel and cannot be updated temporarily
GlobalCoreResponseCode.CARDHOLDERAPPLY_UPDATE_HAS_EXIST=There is an ongoing update information for the current cardholder, please complete it before submitting
GlobalCoreResponseCode.CARDHOLDERAPPLY_UPDATE_ERROR=Failed to update cardholder information
GlobalCoreResponseCode.CARD_HOLDER_NOT_EXIT=Cardholder does not exist, please create a cardholder first
GlobalCoreResponseCode.CARD_APPLY_EXIT=The current card is under review, please do not submit it again
GlobalCoreResponseCode.CARD_APPLY_CREATE_ERROR=Failed to create card information
GlobalCoreResponseCode.CARD_APPLY_UPDATE_ERROR=Failed to update card information
GlobalCoreResponseCode.CARD_NOT_EXIT=The card does not exist
GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR=Failed to operate card status
GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO=The current card cannot perform this operation
GlobalCoreResponseCode.AIR_REQUEST_STATUS_NOT_MATCH=The status is abnormal after the operation
GlobalCoreResponseCode.COMPANY_ACCOUNT_NOT_EXIT=The corporate account does not exist
GlobalCoreResponseCode.COMPANY_ACCOUNT_STATUS_ERROR=The corporate account is not available
GlobalCoreResponseCode.COMPANY_ACCOUNT_BALANCE_NOT_ENOUGH=Insufficient available balance in the corporate account
GlobalCoreResponseCode.CARD_HOLDER_NOT_PASS=Your cardholder information has been synchronized with the enterprise administrator. Please contact the enterprise administrator to review your cardholder information before creating a physical card. The creation of the physical card can only be done after the approval of the review.
IN_PARAMS_NOT_EMPTY=The input parameter cannot be empty
COLL_SIZE_NOT_EMPTY=The collection cannot be empty and its size should be greater than 0
NO_FIND_RECORDS=No records found
CM_DJ_JY_ZT_CAN_ENABLE=The frozen/disabled status can be enabled
CM_SXZ_CAN_DISABLE=Only active cards can be disabled
CM_SHZ_SXZ_DJ_CAN_ZX=Only cards in review/active/frozen status can be cancelled
CAM_NOT_EMPTY=Cannot be empty
i18n.wuxu.hexiao=No need Write-off
i18n.yi.tuikuan=Refunded
i18n.bufen.daihexiao=Amount for Write-off
i18n.dai.hexiao=Amount for Write-off
i18n.hexiao.zhong=Under Approve
i18n.yi.hexiao=Approved
i18n.weizhi=Unknown
i18n.yuding=Authorization
i18n.xiaofei=Consumption
i18n.tuikuan=Refund
i18n.yuding.shifang=Reversal
i18n.jiaoyi.shibai=Failed
i18n.huankuan=Repayment
i18n.qiye.fafang=Distributed
i18n.tuihuan.edu=Return
ActiveModelEnum.NORMAL=Normal Mode
ActiveModelEnum.PETTY=Reserve Fund
CardModelEnum.UN_KNOW=Unknown Operation
CardModelEnum.NORMAL=Normal Mode
CardModelEnum.PETTY=Reserve Fund
CreditApplyStatusEnum.SUCCESS=Success
CreditApplyStatusEnum.FAIL=Failed
CreditApplyStatusEnum.PROCESS=Distributing
CreditApplyStatusEnum.INIT=Waiting Distribute
CardFlowOperationTypeEnum.UNKNOWN=Unknown
CardFlowOperationTypeEnum.CREDIT_APPLY=Request
CardFlowOperationTypeEnum.CREDIT_RETURN=Return
CardFlowOperationTypeEnum.FROZEN=Inactive
CardFlowOperationTypeEnum.UNFROZEN=Active
CardFlowOperationTypeEnum.CONSUME=Consumption
CardFlowOperationTypeEnum.REFUND=Refund
CardFlowOperationTypeEnum.RECTIFICATION=Reverse
CardFlowOperationTypeEnum.REPAYMENT=Repayment
CardFlowOperationTypeEnum.CREDIT_COMPANY_RECYCLE=Recycle
CardFlowOperationTypeEnum.CREDIT_SYSTEM_RECYCLE=Recycle
CardFlowOperationTypeEnum.REPAYMENT_RETURN=Repayment Return
i18n.cunzaishenpizhong.fail=Applications in the approval process are not allowed to submit new applications
i18n.cunzaiweihexiao.fail=Unsettled documents prevent the submission of new applications
i18n.apply.success=Success
cost.none=Create Expense
cost.bind=Expense created
cost.not.commit=Write-off Form created
cost.pending=Under Approve
cost.pass=Approved
i18n.trade.yushouquan=Authorization
i18n.trade.yushouquanchexiao=Authorization
i18n.trade.jiaoyishibai=Failed
i18n.trade.xiaofei=Consumption
i18n.trade.tuikuan=Refund
i18n.trade.cuohuahuankuan=Repayment
